/**
 * Redis Mock Setup for Tests
 * 
 * Comprehensive Redis mocking to fix rate limiting and caching issues
 */

import { vi } from 'vitest';

/**
 * Create a comprehensive Redis mock that works with rate-limit-redis
 */
export function createComprehensiveRedisMock() {
  const mockRedis = {
    // Basic Redis commands
    get: vi.fn().mockResolvedValue(null),
    set: vi.fn().mockResolvedValue('OK'),
    del: vi.fn().mockResolvedValue(1),
    exists: vi.fn().mockResolvedValue(0),
    incr: vi.fn().mockResolvedValue(1),
    expire: vi.fn().mockResolvedValue(1),
    ttl: vi.fn().mockResolvedValue(-1),
    
    // List operations
    lpush: vi.fn().mockResolvedValue(1),
    ltrim: vi.fn().mockResolvedValue('OK'),
    lrange: vi.fn().mockResolvedValue([]),
    
    // Hash operations
    hget: vi.fn().mockResolvedValue(null),
    hset: vi.fn().mockResolvedValue(1),
    hdel: vi.fn().mockResolvedValue(1),
    hgetall: vi.fn().mockResolvedValue({}),
    
    // Set operations
    sadd: vi.fn().mockResolvedValue(1),
    srem: vi.fn().mockResolvedValue(1),
    smembers: vi.fn().mockResolvedValue([]),
    sismember: vi.fn().mockResolvedValue(0),
    
    // Script operations (critical for rate-limit-redis)
    script: vi.fn().mockImplementation((command, ...args) => {
      if (command === 'LOAD') {
        return Promise.resolve('sha1-hash-mock');
      }
      return Promise.resolve('OK');
    }),
    
    // Eval operations
    eval: vi.fn().mockResolvedValue([0, 1000]), // [current_count, ttl]
    evalsha: vi.fn().mockResolvedValue([0, 1000]),
    
    // Connection management
    connect: vi.fn().mockResolvedValue(undefined),
    disconnect: vi.fn().mockResolvedValue(undefined),
    quit: vi.fn().mockResolvedValue('OK'),
    ping: vi.fn().mockResolvedValue('PONG'),
    
    // Pipeline support
    pipeline: vi.fn().mockReturnValue({
      get: vi.fn().mockReturnThis(),
      set: vi.fn().mockReturnThis(),
      del: vi.fn().mockReturnThis(),
      incr: vi.fn().mockReturnThis(),
      expire: vi.fn().mockReturnThis(),
      exec: vi.fn().mockResolvedValue([['OK'], ['OK']]),
    }),
    
    // Multi support
    multi: vi.fn().mockReturnValue({
      get: vi.fn().mockReturnThis(),
      set: vi.fn().mockReturnThis(),
      del: vi.fn().mockReturnThis(),
      incr: vi.fn().mockReturnThis(),
      expire: vi.fn().mockReturnThis(),
      exec: vi.fn().mockResolvedValue([['OK'], ['OK']]),
    }),
    
    // Event emitter methods
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
    removeAllListeners: vi.fn(),
    
    // Status properties
    status: 'ready',
    options: {},
    
    // Custom sendCommand method for rate-limit-redis compatibility
    sendCommand: vi.fn().mockImplementation((command, ...args) => {
      switch (command) {
        case 'SCRIPT':
          if (args[0] === 'LOAD') {
            return Promise.resolve('sha1-hash-mock');
          }
          return Promise.resolve('OK');
        case 'EVALSHA':
          return Promise.resolve([0, 1000]); // [current_count, ttl]
        case 'EVAL':
          return Promise.resolve([0, 1000]);
        case 'GET':
          return Promise.resolve(null);
        case 'SET':
          return Promise.resolve('OK');
        case 'INCR':
          return Promise.resolve(1);
        case 'EXPIRE':
          return Promise.resolve(1);
        default:
          return Promise.resolve('OK');
      }
    }),
    
    // Duplicate method for compatibility
    call: vi.fn().mockImplementation(function(...args) {
      return this.sendCommand(...args);
    }),
  };
  
  // Make sure all methods return the mock instance for chaining
  Object.keys(mockRedis).forEach(key => {
    if (typeof mockRedis[key] === 'function' && !key.includes('mock')) {
      const originalFn = mockRedis[key];
      mockRedis[key] = vi.fn().mockImplementation(function(...args) {
        const result = originalFn.apply(this, args);
        // If it's a promise, return it; otherwise return the mock for chaining
        return result && typeof result.then === 'function' ? result : mockRedis;
      });
    }
  });
  
  return mockRedis;
}

/**
 * Setup Redis mocks for all tests
 */
export function setupRedisMocks() {
  const mockRedis = createComprehensiveRedisMock();
  
  // Mock ioredis
  vi.mock('ioredis', () => {
    const Redis = vi.fn().mockImplementation(() => mockRedis);
    Redis.prototype = mockRedis;
    return { default: Redis, Redis };
  });
  
  // Mock redis (node-redis)
  vi.mock('redis', () => ({
    createClient: vi.fn().mockReturnValue(mockRedis),
    RedisClientType: mockRedis,
  }));
  
  return mockRedis;
}

/**
 * Reset all Redis mocks
 */
export function resetRedisMocks(mockRedis) {
  Object.keys(mockRedis).forEach(key => {
    if (mockRedis[key] && typeof mockRedis[key].mockReset === 'function') {
      mockRedis[key].mockReset();
    }
  });
  
  // Reset to default implementations
  mockRedis.get.mockResolvedValue(null);
  mockRedis.set.mockResolvedValue('OK');
  mockRedis.del.mockResolvedValue(1);
  mockRedis.exists.mockResolvedValue(0);
  mockRedis.incr.mockResolvedValue(1);
  mockRedis.expire.mockResolvedValue(1);
  mockRedis.script.mockImplementation((command, ...args) => {
    if (command === 'LOAD') {
      return Promise.resolve('sha1-hash-mock');
    }
    return Promise.resolve('OK');
  });
  mockRedis.eval.mockResolvedValue([0, 1000]);
  mockRedis.evalsha.mockResolvedValue([0, 1000]);
  mockRedis.sendCommand.mockImplementation((command, ...args) => {
    switch (command) {
      case 'SCRIPT':
        if (args[0] === 'LOAD') {
          return Promise.resolve('sha1-hash-mock');
        }
        return Promise.resolve('OK');
      case 'EVALSHA':
        return Promise.resolve([0, 1000]);
      case 'EVAL':
        return Promise.resolve([0, 1000]);
      default:
        return Promise.resolve('OK');
    }
  });
}

/**
 * Create rate limit specific Redis mock
 */
export function createRateLimitRedisMock() {
  const mockRedis = createComprehensiveRedisMock();
  
  // Override specific methods for rate limiting
  mockRedis.script.mockImplementation((command, scriptContent) => {
    if (command === 'LOAD') {
      // Return a consistent hash for the script
      return Promise.resolve('sha1-rate-limit-script');
    }
    return Promise.resolve('OK');
  });
  
  mockRedis.evalsha.mockImplementation((sha, numKeys, ...args) => {
    // Mock rate limit script response: [current_count, ttl_ms]
    const currentCount = 1;
    const ttlMs = 60000; // 1 minute
    return Promise.resolve([currentCount, ttlMs]);
  });
  
  mockRedis.sendCommand.mockImplementation((command, ...args) => {
    switch (command) {
      case 'SCRIPT':
        if (args[0] === 'LOAD') {
          return Promise.resolve('sha1-rate-limit-script');
        }
        return Promise.resolve('OK');
      case 'EVALSHA':
        return Promise.resolve([1, 60000]); // [current_count, ttl_ms]
      case 'EVAL':
        return Promise.resolve([1, 60000]);
      default:
        return Promise.resolve('OK');
    }
  });
  
  return mockRedis;
}

// Global setup
let globalRedisMock = null;

export function getGlobalRedisMock() {
  if (!globalRedisMock) {
    globalRedisMock = setupRedisMocks();
  }
  return globalRedisMock;
}

export function resetGlobalRedisMock() {
  if (globalRedisMock) {
    resetRedisMocks(globalRedisMock);
  }
}
