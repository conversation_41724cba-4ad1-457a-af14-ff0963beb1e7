
<!DOCTYPE html>
<html>
<head>
    <title>Test Coverage Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 8px; }
        .metric { display: inline-block; margin: 10px; padding: 15px; border-radius: 5px; min-width: 120px; text-align: center; }
        .excellent { background: #d4edda; color: #155724; }
        .good { background: #d1ecf1; color: #0c5460; }
        .fair { background: #fff3cd; color: #856404; }
        .poor { background: #f8d7da; color: #721c24; }
        .recommendations { margin-top: 20px; }
        .recommendation { background: #e9ecef; padding: 10px; margin: 5px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Test Coverage Report</h1>
        <p>Generated: 2025-05-25T14:13:31.878Z</p>
        <p>Total Files: 2</p>
    </div>
    
    <h2>Coverage Metrics</h2>
    <div class="metric excellent">
        <h3>Statements</h3>
        <p>100%</p>
    </div>
    <div class="metric excellent">
        <h3>Functions</h3>
        <p>100%</p>
    </div>
    <div class="metric excellent">
        <h3>Branches</h3>
        <p>100%</p>
    </div>
    <div class="metric excellent">
        <h3>Lines</h3>
        <p>100%</p>
    </div>
    
    <div class="recommendations">
        <h2>Recommendations</h2>
        <div class="recommendation">Coverage meets all thresholds! Consider increasing thresholds for even better quality.</div>
    </div>
</body>
</html>