/**
 * Asset Processing Worker Tests
 *
 * This file contains tests for the asset processing worker.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AssetProcessingQueue } from '../../services/asset/asset-processing-queue.js';
import { createClient } from '@supabase/supabase-js';
import { Storage } from '@google-cloud/storage';
import fs from 'fs';
import { Buffer } from 'node:buffer';

// Mock dependencies
vi.mock('../../services/asset/asset-processing-queue', () => ({
  AssetProcessingQueue: {
    getNextJob: vi.fn(),
    completeJob: vi.fn(),
    getQueueStatus: vi.fn(),
  },
}));

vi.mock('@supabase/supabase-js', () => ({
  createClient: vi.fn(),
}));

vi.mock('@google-cloud/storage', () => ({
  Storage: vi.fn().mockImplementation(() => ({
    bucket: vi.fn().mockReturnValue({
      file: vi.fn().mockReturnValue({
        exists: vi.fn(),
        download: vi.fn(),
        save: vi.fn(),
        getSignedUrl: vi.fn(),
      }),
      upload: vi.fn(),
    }),
  })),
}));

vi.mock('fs', () => ({
  promises: {
    writeFile: vi.fn(),
    readFile: vi.fn(),
    unlink: vi.fn(),
    mkdir: vi.fn({ recursive: true }),
  },
  existsSync: vi.fn(),
  mkdirSync: vi.fn(),
}));

vi.mock('child_process', () => ({
  execSync: vi.fn(),
}));

// Import the module under test
import { processNextAsset, processAsset } from '../../services/asset/asset-processing-worker.js';

describe('Asset Processing Worker', () => {
  // Define types for mocks
  type MockSupabase = {
    from: ReturnType<typeof vi.fn>;
    select: ReturnType<typeof vi.fn>;
    update: ReturnType<typeof vi.fn>;
    eq: ReturnType<typeof vi.fn>;
    storage: {
      from: ReturnType<typeof vi.fn>;
    };
  };

  type MockStorage = {
    bucket: ReturnType<typeof vi.fn>;
  };

  type MockBucket = {
    file: ReturnType<typeof vi.fn>;
    upload: ReturnType<typeof vi.fn>;
  };

  type MockFile = {
    exists: ReturnType<typeof vi.fn>;
    download: ReturnType<typeof vi.fn>;
    save: ReturnType<typeof vi.fn>;
    getSignedUrl: ReturnType<typeof vi.fn>;
  };

  let mockSupabase: MockSupabase;
  let mockStorage: MockStorage;
  let mockBucket: MockBucket;
  let mockFile: MockFile;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Setup mock Supabase client
    mockSupabase = {
      from: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      storage: {
        from: vi.fn().mockReturnValue({
          upload: vi.fn().mockResolvedValue({ data: { path: 'test-path' }, error: null }),
          download: vi.fn().mockResolvedValue({ data: Buffer.from('test'), error: null }),
          getPublicUrl: vi
            .fn()
            .mockReturnValue({ data: { publicUrl: 'https://example.com/test' } }),
        }),
      },
    };

    (createClient as ReturnType<typeof vi.fn>).mockReturnValue(mockSupabase);

    // Setup mock Google Cloud Storage
    mockFile = {
      exists: vi.fn().mockResolvedValue([true]),
      download: vi.fn().mockResolvedValue([Buffer.from('test')]),
      save: vi.fn().mockResolvedValue(),
      getSignedUrl: vi.fn().mockResolvedValue(['https://example.com/test']),
    };

    mockBucket = {
      file: vi.fn().mockReturnValue(mockFile),
      upload: vi.fn().mockResolvedValue([{ name: 'test' }]),
    };

    mockStorage = {
      bucket: vi.fn().mockReturnValue(mockBucket),
    };

    // Mock fs
    const mockFs = vi.mocked(fs);
    mockFs.promises.writeFile = vi.fn().mockResolvedValue(undefined);
    mockFs.promises.readFile = vi.fn().mockResolvedValue(Buffer.from('test'));
    mockFs.promises.unlink = vi.fn().mockResolvedValue(undefined);
    mockFs.existsSync = vi.fn().mockReturnValue(true);
  });

  describe('processNextAsset', () => {
    it('should process the next asset in the queue', async () => {
      // Mock job
      const mockJob = {
        id: 'job-123',
        assetId: 'asset-123',
        status: 'queued',
      };

      // Mock getNextJob to return a job
      (AssetProcessingQueue.getNextJob as ReturnType<typeof vi.fn>).mockResolvedValue(mockJob);

      // Mock processAsset to succeed
      const processAssetSpy = vi.fn().mockResolvedValue({
        success: true,
        optimizedUrl: 'https://example.com/optimized',
        thumbnailUrl: 'https://example.com/thumbnail',
      });

      // Call the function
      await processNextAsset();

      // Assertions
      expect(AssetProcessingQueue.getNextJob).toHaveBeenCalled();
      expect(processAssetSpy).toHaveBeenCalledWith('asset-123');
      expect(AssetProcessingQueue.completeJob).toHaveBeenCalledWith('job-123', 'completed', {
        success: true,
        optimizedUrl: 'https://example.com/optimized',
        thumbnailUrl: 'https://example.com/thumbnail',
      });
    });

    it('should handle processing failure', async () => {
      // Mock job
      const mockJob = {
        id: 'job-123',
        assetId: 'asset-123',
        status: 'queued',
      };

      // Mock getNextJob to return a job
      (AssetProcessingQueue.getNextJob as ReturnType<typeof vi.fn>).mockResolvedValue(mockJob);

      // Mock processAsset to fail
      const processAssetSpy = vi.fn().mockRejectedValue(new Error('Processing failed'));

      // Call the function
      await processNextAsset();

      // Assertions
      expect(AssetProcessingQueue.getNextJob).toHaveBeenCalled();
      expect(processAssetSpy).toHaveBeenCalledWith('asset-123');
      expect(AssetProcessingQueue.completeJob).toHaveBeenCalledWith('job-123', 'failed', {
        success: false,
        error: 'Processing failed',
      });
    });

    it('should do nothing if queue is empty', async () => {
      // Mock getNextJob to return null (empty queue)
      (AssetProcessingQueue.getNextJob as ReturnType<typeof vi.fn>).mockResolvedValue(null);

      // Call the function
      await processNextAsset();

      // Assertions
      expect(AssetProcessingQueue.getNextJob).toHaveBeenCalled();
      expect(AssetProcessingQueue.completeJob).not.toHaveBeenCalled();
    });
  });

  describe('processAsset', () => {
    it('should process an asset successfully', async () => {
      // Mock asset data
      const assetData = {
        id: 'asset-123',
        name: 'model.glb',
        path: 'assets/user-123/model.glb',
        type: 'model/gltf-binary',
        user_id: 'user-123',
      };

      // Mock Supabase response
      mockSupabase
        .from()
        .select()
        .eq()
        .mockResolvedValue({
          data: [assetData],
          error: null,
        });

      // Mock optimizeModel
      const optimizeModelMock = vi.fn().mockResolvedValue({
        success: true,
        path: 'optimized/user-123/model.glb',
      });

      // Apply mock to the module
      vi.mock(
        '../../services/asset/asset-processing-worker.js',
        async (importOriginal: () => Promise<Record<string, unknown>>) => {
          const originalModule = await importOriginal();
          return {
            ...originalModule,
            optimizeModel: optimizeModelMock,
          };
        },
      );

      // Mock generateThumbnail
      const generateThumbnailMock = vi.fn().mockResolvedValue({
        success: true,
        path: 'thumbnails/user-123/model.jpg',
      });

      // Mock validateModel
      const validateModelMock = vi.fn().mockResolvedValue({
        valid: true,
        stats: { vertices: 1000, triangles: 500 },
      });

      // Update the mock to include all functions
      vi.mock(
        '../../services/asset/asset-processing-worker.js',
        async (importOriginal: () => Promise<Record<string, unknown>>) => {
          const originalModule = await importOriginal();
          return {
            ...originalModule,
            optimizeModel: optimizeModelMock,
            generateThumbnail: generateThumbnailMock,
            validateModel: validateModelMock,
          };
        },
      );

      // Call the function
      const result = await processAsset('asset-123');

      // Assertions
      expect(mockSupabase.from).toHaveBeenCalledWith('assets');
      expect(mockSupabase.from().select).toHaveBeenCalled();
      expect(mockSupabase.from().select().eq).toHaveBeenCalledWith('id', 'asset-123');
      expect(optimizeModelMock).toHaveBeenCalled();
      expect(generateThumbnailMock).toHaveBeenCalled();
      expect(validateModelMock).toHaveBeenCalled();
      expect(mockSupabase.from().update).toHaveBeenCalled();
      expect(result).toEqual({
        success: true,
        optimizedUrl: expect.any(String),
        thumbnailUrl: expect.any(String),
        stats: { vertices: 1000, triangles: 500 },
      });
    });

    it('should handle asset not found', async () => {
      // Mock Supabase response
      mockSupabase.from().select().eq().mockResolvedValue({
        data: [],
        error: null,
      });

      // Call the function and expect error
      await expect(processAsset('asset-123')).rejects.toThrow('Asset not found');

      // Assertions
      expect(mockSupabase.from).toHaveBeenCalledWith('assets');
      expect(mockSupabase.from().select).toHaveBeenCalled();
      expect(mockSupabase.from().select().eq).toHaveBeenCalledWith('id', 'asset-123');
    });
  });
});
