/**
 * Mock Factory for Test Setup
 * Provides common mock implementations for testing
 */
import { vi } from 'vitest';

export class MockFactory {
  static createMockRequest(overrides = {}) {
    return {
      method: 'GET',
      url: '/test',
      headers: {},
      body: {},
      query: {},
      params: {},
      cookies: {},
      session: {},
      user: null,
      ip: '127.0.0.1',
      ...overrides,
    };
  }

  static createMockResponse() {
    const mockResponse = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn().mockReturnThis(),
      send: vi.fn().mockReturnThis(),
      set: vi.fn().mockReturnThis(),
      cookie: vi.fn().mockReturnThis(),
      clearCookie: vi.fn().mockReturnThis(),
      redirect: vi.fn().mockReturnThis(),
      end: vi.fn().mockReturnThis(),
      statusCode: 200,
      locals: {},
    };

    // Chain methods properly
    mockResponse.status.mockImplementation(code => {
      mockResponse.statusCode = code;
      return mockResponse;
    });

    return mockResponse;
  }

  static createMockSupabaseClient() {
    const mockFrom = vi.fn().mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: null, error: null }),
        }),
        limit: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: null, error: null }),
        }),
      }),
      insert: vi.fn().mockResolvedValue({ data: null, error: null }),
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({ data: null, error: null }),
      }),
      delete: vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({ data: null, error: null }),
      }),
    });

    return {
      from: mockFrom,
      auth: {
        getUser: vi.fn().mockResolvedValue({ data: { user: null }, error: null }),
        signInWithPassword: vi
          .fn()
          .mockResolvedValue({ data: { user: null, session: null }, error: null }),
        signOut: vi.fn().mockResolvedValue({ error: null }),
        refreshSession: vi.fn().mockResolvedValue({ data: { session: null }, error: null }),
      },
      rpc: vi.fn().mockResolvedValue({ data: null, error: null }),
    };
  }

  static createMockRedisClient() {
    return {
      get: vi.fn().mockResolvedValue(null),
      set: vi.fn().mockResolvedValue('OK'),
      del: vi.fn().mockResolvedValue(1),
      exists: vi.fn().mockResolvedValue(0),
      expire: vi.fn().mockResolvedValue(1),
      incr: vi.fn().mockResolvedValue(1),
      decr: vi.fn().mockResolvedValue(0),
      hget: vi.fn().mockResolvedValue(null),
      hset: vi.fn().mockResolvedValue(1),
      hdel: vi.fn().mockResolvedValue(1),
      sadd: vi.fn().mockResolvedValue(1),
      srem: vi.fn().mockResolvedValue(1),
      smembers: vi.fn().mockResolvedValue([]),
      eval: vi.fn().mockResolvedValue(null),
      quit: vi.fn().mockResolvedValue('OK'),
    };
  }

  static createMockLogger() {
    return {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
      trace: vi.fn(),
    };
  }

  static createMockNextFunction() {
    return vi.fn();
  }

  static createMockJWT() {
    return {
      sign: vi.fn().mockReturnValue('mock-jwt-token'),
      verify: vi.fn().mockReturnValue({ userId: 'test-user', role: 'vendor' }),
      decode: vi.fn().mockReturnValue({ userId: 'test-user', role: 'vendor' }),
    };
  }

  static createMockExpress() {
    return {
      Router: vi.fn().mockReturnValue({
        get: vi.fn(),
        post: vi.fn(),
        put: vi.fn(),
        delete: vi.fn(),
        use: vi.fn(),
      }),
      json: vi.fn(),
      urlencoded: vi.fn(),
      static: vi.fn(),
    };
  }
}

/**
 * Create test context with common utilities
 */
export function createTestContext(overrides = {}) {
  const config = {
    environment: 'test',
    database: {
      url: 'postgresql://test:test@localhost:5432/test_db',
      maxConnections: 5,
    },
    redis: {
      url: 'redis://localhost:6379/1',
      maxRetries: 3,
    },
    api: {
      baseUrl: 'http://localhost:3000',
      timeout: 10000,
    },
    ...overrides,
  };

  return {
    config,
    mocks: {
      request: MockFactory.createMockRequest(),
      response: MockFactory.createMockResponse(),
      supabase: MockFactory.createMockSupabaseClient(),
      redis: MockFactory.createMockRedisClient(),
      logger: MockFactory.createMockLogger(),
      jwt: MockFactory.createMockJWT(),
      express: MockFactory.createMockExpress(),
    },
    cleanup: [],

    addCleanup(fn) {
      this.cleanup.push(fn);
    },

    async teardown() {
      for (const cleanupFn of this.cleanup.reverse()) {
        try {
          await cleanupFn();
        } catch (error) {
          console.error('Cleanup error:', error);
        }
      }
      this.cleanup = [];
    },
  };
}

export default MockFactory;
