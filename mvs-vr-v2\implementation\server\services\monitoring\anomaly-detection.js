/**
 * Anomaly Detection Service
 * Monitors system metrics and detects anomalies
 */

class AnomalyDetectionService {
  constructor(options = {}) {
    this.thresholds = {
      responseTime: options.responseTimeThreshold || 1000,
      errorRate: options.errorRateThreshold || 0.05,
      cpuUsage: options.cpuUsageThreshold || 80,
      memoryUsage: options.memoryUsageThreshold || 85,
      ...options.thresholds
    };
    
    this.metrics = new Map();
    this.anomalies = [];
    this.isMonitoring = false;
  }

  /**
   * Start anomaly detection monitoring
   */
  start() {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;
    console.log('🔍 Anomaly Detection Service started');
    
    // Start monitoring interval
    this.monitoringInterval = setInterval(() => {
      this.checkForAnomalies();
    }, 30000); // Check every 30 seconds
  }

  /**
   * Stop anomaly detection monitoring
   */
  stop() {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    
    console.log('🛑 Anomaly Detection Service stopped');
  }

  /**
   * Record a metric value
   */
  recordMetric(name, value, timestamp = new Date()) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const metricHistory = this.metrics.get(name);
    metricHistory.push({ value, timestamp });
    
    // Keep only last 100 entries
    if (metricHistory.length > 100) {
      metricHistory.shift();
    }
  }

  /**
   * Check for anomalies in recorded metrics
   */
  checkForAnomalies() {
    const currentTime = new Date();
    
    for (const [metricName, history] of this.metrics) {
      if (history.length < 5) continue; // Need at least 5 data points
      
      const recentValues = history.slice(-10).map(entry => entry.value);
      const average = recentValues.reduce((sum, val) => sum + val, 0) / recentValues.length;
      const latestValue = recentValues[recentValues.length - 1];
      
      // Check if latest value exceeds threshold
      const threshold = this.thresholds[metricName];
      if (threshold && latestValue > threshold) {
        this.recordAnomaly({
          type: 'threshold_exceeded',
          metric: metricName,
          value: latestValue,
          threshold,
          timestamp: currentTime,
          severity: this.calculateSeverity(latestValue, threshold)
        });
      }
      
      // Check for sudden spikes (value > 2x average)
      if (latestValue > average * 2) {
        this.recordAnomaly({
          type: 'sudden_spike',
          metric: metricName,
          value: latestValue,
          average,
          timestamp: currentTime,
          severity: 'medium'
        });
      }
    }
  }

  /**
   * Record an anomaly
   */
  recordAnomaly(anomaly) {
    this.anomalies.push(anomaly);
    
    // Keep only last 50 anomalies
    if (this.anomalies.length > 50) {
      this.anomalies.shift();
    }
    
    console.warn('🚨 Anomaly detected:', anomaly);
    
    // Emit event for external handlers
    if (this.onAnomalyDetected) {
      this.onAnomalyDetected(anomaly);
    }
  }

  /**
   * Calculate severity based on how much threshold is exceeded
   */
  calculateSeverity(value, threshold) {
    const ratio = value / threshold;
    if (ratio > 3) return 'critical';
    if (ratio > 2) return 'high';
    if (ratio > 1.5) return 'medium';
    return 'low';
  }

  /**
   * Get recent anomalies
   */
  getRecentAnomalies(limit = 10) {
    return this.anomalies.slice(-limit);
  }

  /**
   * Get metric statistics
   */
  getMetricStats(metricName) {
    const history = this.metrics.get(metricName);
    if (!history || history.length === 0) {
      return null;
    }
    
    const values = history.map(entry => entry.value);
    const sum = values.reduce((a, b) => a + b, 0);
    const avg = sum / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);
    
    return {
      count: values.length,
      average: avg,
      minimum: min,
      maximum: max,
      latest: values[values.length - 1],
      trend: this.calculateTrend(values)
    };
  }

  /**
   * Calculate trend direction
   */
  calculateTrend(values) {
    if (values.length < 3) return 'stable';
    
    const recent = values.slice(-5);
    const older = values.slice(-10, -5);
    
    if (older.length === 0) return 'stable';
    
    const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
    const olderAvg = older.reduce((a, b) => a + b, 0) / older.length;
    
    const change = (recentAvg - olderAvg) / olderAvg;
    
    if (change > 0.1) return 'increasing';
    if (change < -0.1) return 'decreasing';
    return 'stable';
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      isMonitoring: this.isMonitoring,
      metricsCount: this.metrics.size,
      anomaliesCount: this.anomalies.length,
      thresholds: this.thresholds
    };
  }

  /**
   * Set anomaly detection callback
   */
  onAnomaly(callback) {
    this.onAnomalyDetected = callback;
  }
}

module.exports = { AnomalyDetectionService };
