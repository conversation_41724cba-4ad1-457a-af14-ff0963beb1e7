/**
 * Directus-Supabase Integration Tests
 *
 * Tests the integration workflows between Directus and Supabase
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { createClient } from '@supabase/supabase-js';

// Mock Supabase client
const mockSupabaseClient = {
  auth: {
    signInWithPassword: vi.fn(),
    signOut: vi.fn(),
    getSession: vi.fn(),
    onAuthStateChange: vi.fn(),
  },
  from: vi.fn(() => {
    const mockQuery = {
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      upsert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn(),
      then: vi.fn(),
    };
    // Add mockResolvedValue to upsert for the tests
    mockQuery.upsert.mockResolvedValue = vi.fn();
    return mockQuery;
  }),
  storage: {
    from: vi.fn(() => ({
      upload: vi.fn(),
      download: vi.fn(),
      remove: vi.fn(),
      list: vi.fn(),
    })),
  },
};

// Mock Directus API client
const mockDirectusClient = {
  request: vi.fn(),
  items: vi.fn(() => ({
    readByQuery: vi.fn(),
    createOne: vi.fn(),
    updateOne: vi.fn(),
    deleteOne: vi.fn(),
  })),
  auth: {
    login: vi.fn(),
    logout: vi.fn(),
    refresh: vi.fn(),
  },
};

// Mock integration service
class DirectusSupabaseIntegration {
  constructor(directusClient, supabaseClient) {
    this.directus = directusClient;
    this.supabase = supabaseClient;
  }

  async syncVendorData(vendorId) {
    try {
      // Fetch vendor data from Directus
      const directusVendor = await this.directus.items('vendors').readByQuery({
        filter: { id: { _eq: vendorId } },
      });

      if (!directusVendor.data || directusVendor.data.length === 0) {
        throw new Error('Vendor not found in Directus');
      }

      const vendor = directusVendor.data[0];

      // Sync to Supabase
      const { data, error } = await this.supabase.from('vendors').upsert({
        id: vendor.id,
        email: vendor.email,
        name: vendor.name,
        status: vendor.status,
        updated_at: new Date().toISOString(),
      });

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error syncing vendor data:', error);
      throw error;
    }
  }

  async syncAssetData(assetId) {
    try {
      // Fetch asset data from Directus
      const directusAsset = await this.directus.items('assets').readByQuery({
        filter: { id: { _eq: assetId } },
      });

      if (!directusAsset.data || directusAsset.data.length === 0) {
        throw new Error('Asset not found in Directus');
      }

      const asset = directusAsset.data[0];

      // Sync to Supabase
      const { data, error } = await this.supabase.from('assets').upsert({
        id: asset.id,
        name: asset.name,
        vendor_id: asset.vendor_id,
        file_url: asset.file_url,
        status: asset.status,
        metadata: asset.metadata,
        updated_at: new Date().toISOString(),
      });

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error syncing asset data:', error);
      throw error;
    }
  }

  async handleAuthSync(directusToken) {
    try {
      // Validate Directus token and get user info
      const userResponse = await this.directus.request('/users/me', {
        headers: {
          Authorization: `Bearer ${directusToken}`,
        },
      });

      if (!userResponse.data) {
        throw new Error('Invalid Directus token');
      }

      const user = userResponse.data;

      // Create or update user in Supabase
      const { data, error } = await this.supabase.auth.signInWithPassword({
        email: user.email,
        password: 'temp-password', // In real implementation, use proper auth flow
      });

      if (error && error.message !== 'Invalid login credentials') {
        throw error;
      }

      return { user: data?.user, session: data?.session };
    } catch (error) {
      console.error('Error syncing auth:', error);
      throw error;
    }
  }
}

describe('Directus-Supabase Integration Tests', () => {
  let integration;

  
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup Directus mock with proper chaining
    const mockReadByQuery = vi.fn();
    mockDirectusClient.items.mockReturnValue({
      readByQuery: mockReadByQuery,
      createOne: vi.fn(),
      updateOne: vi.fn(),
      deleteOne: vi.fn(),
    });
    
    // Setup Supabase mock with proper chaining
    const mockUpsert = vi.fn();
    mockSupabaseClient.from.mockReturnValue({
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      upsert: mockUpsert,
      update: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockReturnThis(),
    });
    
    integration = new DirectusSupabaseIntegration(mockDirectusClient, mockSupabaseClient);
  });

    mockSupabaseClient.from.mockReturnValue({
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      upsert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockReturnThis(),
    });

    integration = new DirectusSupabaseIntegration(mockDirectusClient, mockSupabaseClient);
  });

  describe('Vendor Data Synchronization', () => {
    it('should sync vendor data from Directus to Supabase', async () => {
      // Mock Directus response
      mockDirectusClient.items().readByQuery.mockResolvedValue({
        data: [
          {
            id: 'vendor-1',
            email: '<EMAIL>',
            name: 'Test Vendor',
            status: 'active',
          },
        ],
      });

      // Mock Supabase response - fix the chaining issue
      const mockUpsert = vi.fn().mockResolvedValue({
        data: [
          {
            id: 'vendor-1',
            email: '<EMAIL>',
            name: 'Test Vendor',
            status: 'active',
          },
        ],
        error: null,
      });

      mockSupabaseClient.from.mockReturnValue({
        upsert: mockUpsert,
        select: vi.fn().mockReturnThis(),
        insert: vi.fn().mockReturnThis(),
        update: vi.fn().mockReturnThis(),
        delete: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockReturnThis(),
      });

      const result = await integration.syncVendorData('vendor-1');

      expect(mockDirectusClient.items).toHaveBeenCalledWith('vendors');
      expect(mockDirectusClient.items().readByQuery).toHaveBeenCalledWith({
        filter: { id: { _eq: 'vendor-1' } },
      });
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('vendors');
      expect(result).toBeDefined();
    });

    it('should handle vendor not found in Directus', async () => {
      mockDirectusClient.items().readByQuery.mockResolvedValue({
        data: [],
      });

      await expect(integration.syncVendorData('nonexistent-vendor')).rejects.toThrow(
        'Vendor not found in Directus',
      );
    });

    it('should handle Supabase sync errors', async () => {
      // Setup Directus mock
      const mockReadByQuery = vi.fn().mockResolvedValue({
        data: [
          {
            id: 'vendor-1',
            email: '<EMAIL>',
            name: 'Test Vendor',
            status: 'active',
          },
        ],
      });

      mockDirectusClient.items.mockReturnValue({
        readByQuery: mockReadByQuery,
      });

      // Setup Supabase mock with error
      const mockUpsert = vi.fn().mockResolvedValue({
        data: null,
        error: new Error('Supabase sync error'),
      });

      mockSupabaseClient.from.mockReturnValue({
        upsert: mockUpsert,
      });

      await expect(integration.syncVendorData('vendor-1')).rejects.toThrow('Supabase sync error');
    });
  });

  describe('Asset Data Synchronization', () => {
    it('should sync asset data from Directus to Supabase', async () => {
      mockDirectusClient.items().readByQuery.mockResolvedValue({
        data: [
          {
            id: 'asset-1',
            name: 'Test Asset',
            vendor_id: 'vendor-1',
            file_url: '/assets/test-asset.jpg',
            status: 'approved',
            metadata: { size: '1024x768' },
          },
        ],
      });

      mockSupabaseClient.from().upsert.mockResolvedValue({
        data: [
          {
            id: 'asset-1',
            name: 'Test Asset',
            vendor_id: 'vendor-1',
          },
        ],
        error: null,
      });

      const result = await integration.syncAssetData('asset-1');

      expect(mockDirectusClient.items).toHaveBeenCalledWith('assets');
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('assets');
      expect(result).toBeDefined();
    });

    it('should handle asset not found in Directus', async () => {
      mockDirectusClient.items().readByQuery.mockResolvedValue({
        data: [],
      });

      await expect(integration.syncAssetData('nonexistent-asset')).rejects.toThrow(
        'Asset not found in Directus',
      );
    });
  });

  describe('Authentication Synchronization', () => {
    it('should sync authentication between Directus and Supabase', async () => {
      mockDirectusClient.request.mockResolvedValue({
        data: {
          id: 'user-1',
          email: '<EMAIL>',
          first_name: 'Test',
          last_name: 'User',
        },
      });

      mockSupabaseClient.auth.signInWithPassword.mockResolvedValue({
        data: {
          user: { id: 'user-1', email: '<EMAIL>' },
          session: { access_token: 'supabase-token' },
        },
        error: null,
      });

      const result = await integration.handleAuthSync('directus-token');

      expect(mockDirectusClient.request).toHaveBeenCalledWith('/users/me', {
        headers: {
          Authorization: 'Bearer directus-token',
        },
      });
      expect(mockSupabaseClient.auth.signInWithPassword).toHaveBeenCalled();
      expect(result.user).toBeDefined();
      expect(result.session).toBeDefined();
    });

    it('should handle invalid Directus token', async () => {
      mockDirectusClient.request.mockResolvedValue({
        data: null,
      });

      await expect(integration.handleAuthSync('invalid-token')).rejects.toThrow(
        'Invalid Directus token',
      );
    });
  });

  describe('Real-time Data Sync', () => {
    it('should handle real-time updates from Directus to Supabase', async () => {
      const mockWebhookPayload = {
        event: 'items.create',
        collection: 'vendors',
        item: {
          id: 'vendor-2',
          email: '<EMAIL>',
          name: 'New Vendor',
          status: 'pending',
        },
      };

      // Simulate webhook handler
      const handleWebhook = async payload => {
        if (payload.collection === 'vendors') {
          return await integration.syncVendorData(payload.item.id);
        }
      };

      mockDirectusClient.items().readByQuery.mockResolvedValue({
        data: [mockWebhookPayload.item],
      });

      mockSupabaseClient.from().upsert.mockResolvedValue({
        data: [mockWebhookPayload.item],
        error: null,
      });

      const result = await handleWebhook(mockWebhookPayload);

      expect(result).toBeDefined();
    });
  });

  describe('Error Recovery and Retry Logic', () => {
    it('should retry failed sync operations', async () => {
      let callCount = 0;
      mockDirectusClient.items().readByQuery.mockImplementation(() => {
        callCount++;
        if (callCount < 3) {
          throw new Error('Network error');
        }
        return Promise.resolve({
          data: [
            {
              id: 'vendor-1',
              email: '<EMAIL>',
              name: 'Test Vendor',
              status: 'active',
            },
          ],
        });
      });

      mockSupabaseClient.from().upsert.mockResolvedValue({
        data: [{ id: 'vendor-1' }],
        error: null,
      });

      // Implement retry logic
      const syncWithRetry = async (vendorId, maxRetries = 3) => {
        for (let i = 0; i < maxRetries; i++) {
          try {
            return await integration.syncVendorData(vendorId);
          } catch (error) {
            if (i === maxRetries - 1) throw error;
            await new Promise(resolve => setTimeout(resolve, 100 * (i + 1)));
          }
        }
      };

      const result = await syncWithRetry('vendor-1');
      expect(result).toBeDefined();
      expect(callCount).toBe(3);
    });
  });
});
