/**
 * Scene Validator Complete Integration Test
 *
 * This test verifies the complete implementation of the scene validator service,
 * including all API endpoints and functionality.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import request from 'supertest';
import express from 'express';
import { SceneValidatorService } from '../../services/scene/scene-validator';
import { validateScene, validateSceneData, validateSceneFlow } from '../../api/scenes/validate';
import {
  analyzeScenePerformance,
  checkSceneCompatibility,
} from '../../api/scenes/validate/[scene_id]';

// Mock dependencies
vi.mock('../../shared/utils/logger', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
  },
}));

// Mock Supabase client
const mockSupabase = {
  from: vi.fn().mockReturnThis(),
  select: vi.fn().mockReturnThis(),
  eq: vi.fn().mockReturnThis(),
  in: vi.fn().mockReturnThis(),
  single: vi.fn(),
};

// Mock SceneValidatorService
vi.mock('../../services/scene/scene-validator');
const MockSceneValidatorService = SceneValidatorService as any;

describe('Scene Validator Complete Integration Test', () => {
  let app: express.Application;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Create Express app
    app = express();
    app.use(express.json());

    // Configure routes
    app.get('/api/scenes/validate/:scene_id', (req, res) => validateScene(req as any, res as any));
    app.post('/api/scenes/validate/data', (req, res) => validateSceneData(req as any, res as any));
    app.post('/api/scenes/validate/flow', (req, res) => validateSceneFlow(req as any, res as any));
    app.get('/api/scenes/validate/:scene_id/performance', (req, res) => {
      req.query.action = 'performance';
      return analyzeScenePerformance(req as any, res as any);
    });
    app.get('/api/scenes/validate/:scene_id/compatibility', (req, res) => {
      req.query.action = 'compatibility';
      return checkSceneCompatibility(req as any, res as any);
    });
  });

  describe('Scene Validation Flow', () => {
    it('should validate a scene, analyze performance, and check compatibility', async () => {
      // Arrange
      const sceneId = 'scene-123';

      // Mock validation result
      const mockValidationResult = {
        valid: true,
        errors: [],
        warnings: [],
      };

      // Mock performance analysis result
      const mockPerformanceResult = {
        impact: 'low',
        metrics: {
          assetCount: 10,
          totalAssetSize: 5000000,
          complexityScore: 50,
          estimatedLoadTime: 2.5,
          estimatedMemoryUsage: 7500000,
        },
        recommendations: [],
      };

      // Mock compatibility result
      const mockCompatibilityResult = {
        compatible: true,
        targetEnvironment: 'quest2',
        issues: [],
      };

      // Setup mocks
      MockSceneValidatorService.prototype.validateScene = vi
        .fn()
        .mockResolvedValue(mockValidationResult);
      MockSceneValidatorService.prototype.analyzeScenePerformance = vi
        .fn()
        .mockResolvedValue(mockPerformanceResult);
      MockSceneValidatorService.prototype.checkSceneCompatibility = vi
        .fn()
        .mockResolvedValue(mockCompatibilityResult);

      // Act & Assert - Validate Scene
      const validationResponse = await request(app).get(`/api/scenes/validate/${sceneId}`);
      expect(validationResponse.status).toBe(200);
      expect(validationResponse.body).toEqual({
        success: true,
        data: mockValidationResult,
      });
      expect(MockSceneValidatorService.prototype.validateScene).toHaveBeenCalledWith(sceneId);

      // Act & Assert - Analyze Performance
      const performanceResponse = await request(app).get(
        `/api/scenes/validate/${sceneId}/performance`,
      );
      expect(performanceResponse.status).toBe(200);
      expect(performanceResponse.body).toEqual({
        success: true,
        data: mockPerformanceResult,
      });
      expect(MockSceneValidatorService.prototype.analyzeScenePerformance).toHaveBeenCalledWith(
        sceneId,
      );

      // Act & Assert - Check Compatibility
      const compatibilityResponse = await request(app).get(
        `/api/scenes/validate/${sceneId}/compatibility`,
      );
      expect(compatibilityResponse.status).toBe(200);
      expect(compatibilityResponse.body).toEqual({
        success: true,
        data: mockCompatibilityResult,
      });
      expect(MockSceneValidatorService.prototype.checkSceneCompatibility).toHaveBeenCalledWith(
        sceneId,
        undefined,
      );
    });
  });

  describe('Scene Data Validation', () => {
    it('should validate scene data', async () => {
      // Arrange
      const mockData = {
        objects: [],
        settings: {},
      };

      MockSceneValidatorService.prototype.validateSceneData = vi.fn().mockReturnValue([]);

      // Act
      const response = await request(app)
        .post('/api/scenes/validate/data')
        .send({ data: mockData });

      // Assert
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        data: {
          valid: true,
          errors: [],
        },
      });
      expect(MockSceneValidatorService.prototype.validateSceneData).toHaveBeenCalledWith(mockData);
    });

    it('should return validation errors for invalid data', async () => {
      // Arrange
      const mockData = {
        // Missing required fields
      };

      const mockErrors = [
        {
          code: 'MISSING_OBJECTS',
          message: 'Scene data must have objects',
          path: 'data.objects',
        },
      ];

      MockSceneValidatorService.prototype.validateSceneData = vi.fn().mockReturnValue(mockErrors);

      // Act
      const response = await request(app)
        .post('/api/scenes/validate/data')
        .send({ data: mockData });

      // Assert
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        data: {
          valid: false,
          errors: mockErrors,
        },
      });
    });
  });

  describe('Scene Flow Validation', () => {
    it('should validate scene flow', async () => {
      // Arrange
      const mockFlow = {
        startup: {
          space: 'space-1',
          next: 'node-2',
        },
        'node-2': {
          space: 'space-2',
        },
      };

      const mockValidationResult = {
        valid: true,
        errors: [],
        warnings: [],
      };

      MockSceneValidatorService.prototype.validateSceneFlow = vi
        .fn()
        .mockResolvedValue(mockValidationResult);

      // Act
      const response = await request(app)
        .post('/api/scenes/validate/flow')
        .send({ flow: mockFlow });

      // Assert
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        data: mockValidationResult,
      });
      expect(MockSceneValidatorService.prototype.validateSceneFlow).toHaveBeenCalledWith(mockFlow);
    });

    it('should return validation errors for invalid flow', async () => {
      // Arrange
      const mockFlow = {
        // Missing startup node
        'node-2': {
          space: 'space-2',
        },
      };

      const mockValidationResult = {
        valid: false,
        errors: [
          {
            code: 'NO_STARTUP_NODE',
            message: 'No startup node found in flow',
            path: 'startup',
          },
        ],
        warnings: [],
      };

      MockSceneValidatorService.prototype.validateSceneFlow = vi
        .fn()
        .mockResolvedValue(mockValidationResult);

      // Act
      const response = await request(app)
        .post('/api/scenes/validate/flow')
        .send({ flow: mockFlow });

      // Assert
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        data: mockValidationResult,
      });
    });
  });

  describe('Performance Analysis', () => {
    it('should analyze scene performance', async () => {
      // Arrange
      const sceneId = 'scene-123';

      const mockPerformanceResult = {
        impact: 'medium',
        metrics: {
          assetCount: 50,
          totalAssetSize: 50000000,
          complexityScore: 150,
          estimatedLoadTime: 7.5,
          estimatedMemoryUsage: 75000000,
        },
        recommendations: [
          {
            code: 'LARGE_ASSETS',
            message: 'Total asset size is 50.00 MB, consider optimizing assets',
            priority: 'high',
          },
        ],
      };

      MockSceneValidatorService.prototype.analyzeScenePerformance = vi
        .fn()
        .mockResolvedValue(mockPerformanceResult);

      // Act
      const response = await request(app).get(`/api/scenes/validate/${sceneId}/performance`);

      // Assert
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        data: mockPerformanceResult,
      });
    });
  });

  describe('Compatibility Check', () => {
    it('should check scene compatibility with specified environment', async () => {
      // Arrange
      const sceneId = 'scene-123';
      const targetEnvironment = 'quest3';

      const mockCompatibilityResult = {
        compatible: true,
        targetEnvironment: 'quest3',
        issues: [],
      };

      MockSceneValidatorService.prototype.checkSceneCompatibility = vi
        .fn()
        .mockResolvedValue(mockCompatibilityResult);

      // Act
      const response = await request(app)
        .get(`/api/scenes/validate/${sceneId}/compatibility`)
        .query({ target_environment: targetEnvironment });

      // Assert
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        data: mockCompatibilityResult,
      });
      expect(MockSceneValidatorService.prototype.checkSceneCompatibility).toHaveBeenCalledWith(
        sceneId,
        targetEnvironment,
      );
    });
  });
});
