/**
 * Test Configuration Utilities
 * Provides configuration and setup utilities for tests
 */

const path = require('path');
const fs = require('fs');

/**
 * Default test configuration
 */
const defaultConfig = {
  // Test environment
  environment: 'test',
  
  // Database configuration
  database: {
    url: process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5432/test_db',
    maxConnections: 5,
    timeout: 5000,
  },
  
  // Redis configuration
  redis: {
    url: process.env.TEST_REDIS_URL || 'redis://localhost:6379/1',
    maxRetries: 3,
    timeout: 2000,
  },
  
  // API configuration
  api: {
    baseUrl: process.env.TEST_API_BASE_URL || 'http://localhost:3000',
    timeout: 10000,
    retries: 2,
  },
  
  // Supabase configuration
  supabase: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://hiyqiqbgiueyyvqoqhht.supabase.co',
    anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'test-anon-key',
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY || 'test-service-role-key',
  },
  
  // Test timeouts
  timeouts: {
    unit: 5000,
    integration: 15000,
    e2e: 30000,
  },
  
  // Test data
  testData: {
    users: {
      vendor: {
        email: '<EMAIL>',
        password: 'test123',
        role: 'vendor',
      },
      admin: {
        email: '<EMAIL>',
        password: 'admin123',
        role: 'admin',
      },
    },
    assets: {
      sampleImage: path.join(__dirname, '../fixtures/sample.jpg'),
      sampleModel: path.join(__dirname, '../fixtures/sample.fbx'),
    },
  },
  
  // Mock configuration
  mocks: {
    enabled: true,
    redis: true,
    supabase: true,
    filesystem: true,
  },
  
  // Logging configuration
  logging: {
    level: process.env.TEST_LOG_LEVEL || 'error',
    silent: process.env.TEST_SILENT === 'true',
  },
};

/**
 * Load test configuration
 */
function loadTestConfig(overrides = {}) {
  const config = { ...defaultConfig, ...overrides };
  
  // Validate required environment variables
  const requiredEnvVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  ];
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  if (missingVars.length > 0) {
    console.warn(`Warning: Missing environment variables: ${missingVars.join(', ')}`);
  }
  
  return config;
}

/**
 * Create test database URL
 */
function createTestDatabaseUrl(dbName = 'test_db') {
  const baseUrl = process.env.DATABASE_URL || 'postgresql://postgres:password@localhost:5432';
  const url = new URL(baseUrl);
  url.pathname = `/${dbName}`;
  return url.toString();
}

/**
 * Create test Redis URL
 */
function createTestRedisUrl(dbIndex = 1) {
  const baseUrl = process.env.REDIS_URL || 'redis://localhost:6379';
  const url = new URL(baseUrl);
  url.pathname = `/${dbIndex}`;
  return url.toString();
}

/**
 * Get test fixture path
 */
function getFixturePath(filename) {
  return path.join(__dirname, '../fixtures', filename);
}

/**
 * Check if fixture exists
 */
function fixtureExists(filename) {
  return fs.existsSync(getFixturePath(filename));
}

/**
 * Load test fixture
 */
function loadFixture(filename) {
  const fixturePath = getFixturePath(filename);
  
  if (!fs.existsSync(fixturePath)) {
    throw new Error(`Test fixture not found: ${filename}`);
  }
  
  const ext = path.extname(filename).toLowerCase();
  
  if (ext === '.json') {
    return JSON.parse(fs.readFileSync(fixturePath, 'utf8'));
  }
  
  return fs.readFileSync(fixturePath);
}

/**
 * Create temporary test directory
 */
function createTempDir(prefix = 'test-') {
  const tempDir = path.join(__dirname, '../temp', `${prefix}${Date.now()}`);
  fs.mkdirSync(tempDir, { recursive: true });
  return tempDir;
}

/**
 * Clean up temporary test directory
 */
function cleanupTempDir(tempDir) {
  if (fs.existsSync(tempDir)) {
    fs.rmSync(tempDir, { recursive: true, force: true });
  }
}

/**
 * Wait for condition
 */
async function waitFor(condition, timeout = 5000, interval = 100) {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    if (await condition()) {
      return true;
    }
    
    await new Promise(resolve => setTimeout(resolve, interval));
  }
  
  throw new Error(`Condition not met within ${timeout}ms`);
}

/**
 * Retry function with exponential backoff
 */
async function retry(fn, maxAttempts = 3, baseDelay = 100) {
  let lastError;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxAttempts) {
        break;
      }
      
      const delay = baseDelay * Math.pow(2, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
}

/**
 * Generate random test data
 */
function generateTestData() {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000);
  
  return {
    id: `test-${timestamp}-${random}`,
    email: `test-${timestamp}-${random}@example.com`,
    name: `Test User ${random}`,
    timestamp: new Date().toISOString(),
  };
}

/**
 * Create test context
 */
function createTestContext(overrides = {}) {
  const config = loadTestConfig(overrides);
  
  return {
    config,
    tempDir: null,
    cleanup: [],
    
    // Helper methods
    createTempDir() {
      if (!this.tempDir) {
        this.tempDir = createTempDir();
        this.cleanup.push(() => cleanupTempDir(this.tempDir));
      }
      return this.tempDir;
    },
    
    addCleanup(fn) {
      this.cleanup.push(fn);
    },
    
    async teardown() {
      for (const cleanupFn of this.cleanup.reverse()) {
        try {
          await cleanupFn();
        } catch (error) {
          console.error('Cleanup error:', error);
        }
      }
      this.cleanup = [];
    },
  };
}

module.exports = {
  defaultConfig,
  loadTestConfig,
  createTestDatabaseUrl,
  createTestRedisUrl,
  getFixturePath,
  fixtureExists,
  loadFixture,
  createTempDir,
  cleanupTempDir,
  waitFor,
  retry,
  generateTestData,
  createTestContext,
};
