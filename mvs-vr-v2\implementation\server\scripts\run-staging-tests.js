#!/usr/bin/env node

/**
 * Staging Test Runner
 * Configures and runs tests against the staging server
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 MVS-VR Staging Test Runner');
console.log('================================');

// Switch to staging environment
console.log('🔄 Switching to staging environment...');
try {
  execSync('node scripts/switch-test-env.js staging', {
    stdio: 'inherit',
    cwd: __dirname + '/..',
  });
} catch (error) {
  console.error('❌ Failed to switch to staging environment:', error.message);
  process.exit(1);
}

// Verify staging configuration
console.log('\n🔍 Verifying staging configuration...');
const envPath = path.join(__dirname, '../.env.test');
const envContent = fs.readFileSync(envPath, 'utf8');

if (!envContent.includes('TEST_ENV=staging')) {
  console.error('❌ Failed to switch to staging environment');
  process.exit(1);
}

console.log('✅ Staging environment configured');

// Parse command line arguments
const args = process.argv.slice(2);
const testPattern = args.find(arg => !arg.startsWith('--')) || '';
const isWatch = args.includes('--watch');
const isCoverage = args.includes('--coverage');
const isVerbose = args.includes('--verbose');

// Build test command
let testCommand = 'npm test';

if (testPattern) {
  testCommand += ` -- "${testPattern}"`;
} else {
  testCommand += ' --';
}

if (isWatch) {
  testCommand += ' --watch';
}

if (isCoverage) {
  testCommand += ' --coverage';
}

if (isVerbose) {
  testCommand += ' --reporter=verbose';
}

// Add staging-specific test options (using Vitest config)
testCommand += ' --testTimeout=20000'; // Longer timeout for network requests
testCommand += ' --retry=2'; // Retry failed tests due to network issues

console.log('\n🧪 Running staging tests...');
console.log(`📋 Command: ${testCommand}`);
console.log('🌐 Target: https://hiyqiqbgiueyyvqoqhht.supabase.co (Supabase API)');
console.log('🗄️  Database: https://hiyqiqbgiueyyvqoqhht.supabase.co');

// Run tests
try {
  execSync(testCommand, {
    stdio: 'inherit',
    cwd: __dirname + '/..',
    env: {
      ...process.env,
      NODE_ENV: 'test',
      TEST_ENV: 'staging',
      // Disable mocks for staging tests
      TEST_ENABLE_MOCKS: 'false',
      // Enable detailed logging for staging
      TEST_ENABLE_LOGGING: 'true',
      // Increase timeouts for network requests
      TEST_TIMEOUT: '20000',
      TEST_SETUP_TIMEOUT: '30000',
    },
  });

  console.log('\n✅ Staging tests completed successfully!');
} catch (error) {
  console.error('\n❌ Staging tests failed');
  console.error('Error:', error.message);

  console.log('\n🔧 Troubleshooting tips:');
  console.log('1. Check network connectivity to staging server');
  console.log('2. Verify staging server is running and accessible');
  console.log('3. Check Supabase staging instance status');
  console.log('4. Review test logs for specific error details');
  console.log('5. Try running individual test files to isolate issues');

  process.exit(1);
}

// Show post-test information
console.log('\n📊 Test Results Summary:');
console.log('- Environment: Staging');
console.log('- Server: https://mvs.kanousai.com');
console.log('- Database: https://hiyqiqbgiueyyvqoqhht.supabase.co');
console.log('- Mocks: Disabled (testing real services)');

if (isCoverage) {
  console.log('\n📈 Coverage report generated in ./coverage/');
}

console.log('\n💡 To run specific tests:');
console.log('  node scripts/run-staging-tests.js "test-pattern"');
console.log('\n💡 To run with coverage:');
console.log('  node scripts/run-staging-tests.js --coverage');
console.log('\n💡 To run in watch mode:');
console.log('  node scripts/run-staging-tests.js --watch');
