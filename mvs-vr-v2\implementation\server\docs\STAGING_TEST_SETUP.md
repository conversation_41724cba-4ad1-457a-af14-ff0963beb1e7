# Staging Test Setup Guide

## Overview

This guide explains how to configure and run tests against the staging server environment. The staging environment uses real services and is safe for testing without affecting production data.

## Environment Configuration

### Staging Server Details
- **Server URL**: https://mvs.kanousai.com
- **Supabase Instance**: https://hiyqiqbgiueyyvqoqhht.supabase.co
- **Environment**: Safe for testing (no production data)

### Test Environment Variables

The staging configuration is already set up in `.env.test`:

```env
# Test Environment Selection
TEST_ENV=staging

# Staging Test Configuration
STAGING_SERVER_URL=https://mvs.kanousai.com
STAGING_SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
STAGING_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
STAGING_SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Test User Configuration for Staging
STAGING_TEST_VENDOR_EMAIL=<EMAIL>
STAGING_TEST_VENDOR_PASSWORD=TestPassword123!
STAGING_TEST_ADMIN_EMAIL=<EMAIL>
STAGING_TEST_ADMIN_PASSWORD=AdminPassword123!
```

## Quick Start

### 1. Validate Staging Connection

Before running tests, validate connectivity to the staging environment:

```bash
npm run test:staging:validate
```

This will check:
- ✅ Server connectivity
- ✅ Supabase database connection
- ✅ API endpoint availability
- ✅ Test user validation

### 2. Run Staging Tests

```bash
# Run all tests against staging
npm run test:staging

# Run specific test pattern
npm run test:staging "auth"

# Run with coverage
npm run test:staging:coverage

# Run in watch mode
npm run test:staging:watch
```

## Manual Environment Switching

You can also manually switch environments:

```bash
# Switch to staging
node scripts/switch-test-env.js staging

# Verify current environment
node scripts/switch-test-env.js

# Run tests normally (will use staging)
npm test
```

## Test Configuration Differences

### Staging vs Local

| Aspect | Local | Staging |
|--------|-------|---------|
| **Server** | localhost:3000 | https://mvs.kanousai.com |
| **Database** | Local Supabase | Remote Supabase |
| **Mocks** | Enabled | Disabled |
| **Timeouts** | 5s | 15-20s |
| **Retries** | 2 | 2-3 |
| **Logging** | Detailed | Detailed |

### Staging-Specific Settings

- **Longer Timeouts**: Network requests take longer
- **Real Services**: No mocks, testing actual implementations
- **Safe Data**: Staging database with test data only
- **Network Dependent**: Requires internet connectivity

## Available Test Scripts

### Core Scripts
- `npm run test:staging` - Run all staging tests
- `npm run test:staging:validate` - Validate staging connectivity
- `npm run test:staging:coverage` - Run with coverage report
- `npm run test:staging:watch` - Run in watch mode

### Environment Management
- `node scripts/switch-test-env.js staging` - Switch to staging
- `node scripts/switch-test-env.js local` - Switch back to local
- `node scripts/validate-staging-connection.js` - Validate connection

## Test Categories

### Unit Tests
- Test individual components and functions
- Run against staging APIs and services
- Validate real service integration

### Integration Tests
- Test complete workflows
- Validate end-to-end functionality
- Test with real database and services

### API Tests
- Test all API endpoints
- Validate authentication flows
- Test data persistence

### Security Tests
- Test authentication and authorization
- Validate security middleware
- Test rate limiting and protection

## Troubleshooting

### Common Issues

#### 1. Connection Timeout
```
❌ Server: Timeout (10s)
```
**Solution**: Check network connectivity and staging server status

#### 2. Authentication Failed
```
❌ Supabase query failed: Invalid JWT
```
**Solution**: Verify Supabase keys in `.env.test` are correct

#### 3. Test User Not Found
```
⚠️ Test vendor not found: <EMAIL>
```
**Solution**: Create test users in staging database or update email in config

#### 4. API Endpoint 404
```
❌ Vendors API: 404
```
**Solution**: Verify staging server is running and endpoints are deployed

### Debug Steps

1. **Validate Connection**
   ```bash
   npm run test:staging:validate
   ```

2. **Check Environment**
   ```bash
   node scripts/switch-test-env.js
   ```

3. **Run Single Test**
   ```bash
   npm run test:staging "simple-test"
   ```

4. **Check Logs**
   - Enable verbose logging in test configuration
   - Check staging server logs if accessible

## Best Practices

### 1. Always Validate First
Run `npm run test:staging:validate` before running tests to catch connectivity issues early.

### 2. Use Appropriate Timeouts
Staging tests use longer timeouts (15-20s) to account for network latency.

### 3. Handle Network Issues
Tests include retry logic for network-related failures.

### 4. Clean Test Data
Staging tests should clean up any test data they create.

### 5. Monitor Resource Usage
Be mindful of staging server resources when running extensive test suites.

## Test Data Management

### Test Users
- **Vendor**: <EMAIL>
- **Admin**: <EMAIL>

### Test Data
- Tests should create and clean up their own test data
- Use unique identifiers to avoid conflicts
- Staging database is reset periodically

## CI/CD Integration

### GitHub Actions
```yaml
- name: Run Staging Tests
  run: |
    npm run test:staging:validate
    npm run test:staging:coverage
```

### Environment Variables
Ensure staging credentials are available in CI environment.

## Security Considerations

- Staging uses real but non-production credentials
- Test data should not contain sensitive information
- Staging environment is isolated from production
- Regular security updates and monitoring

## Support

For issues with staging tests:
1. Check this documentation
2. Run validation script
3. Review test logs
4. Contact development team

---

**Last Updated**: December 25, 2024  
**Environment**: Staging (https://mvs.kanousai.com)  
**Database**: https://hiyqiqbgiueyyvqoqhht.supabase.co
