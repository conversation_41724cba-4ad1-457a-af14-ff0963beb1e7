/**
 * Bootstrap Service
 * Handles application initialization and service startup
 */

export interface BootstrapConfig {
  environment: string;
  port: number;
  database: {
    url: string;
    maxConnections: number;
  };
  redis: {
    url: string;
    maxRetries: number;
  };
  services: string[];
}

export interface ServiceStatus {
  name: string;
  status: 'starting' | 'running' | 'stopped' | 'error';
  lastCheck: Date;
  error?: string;
}

export class BootstrapService {
  private config: BootstrapConfig;
  private services: Map<string, ServiceStatus> = new Map();
  private isInitialized = false;

  constructor(config: BootstrapConfig) {
    this.config = config;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    console.log('🚀 Initializing Bootstrap Service...');

    try {
      // Initialize core services
      await this.initializeDatabase();
      await this.initializeRedis();
      await this.initializeServices();

      this.isInitialized = true;
      console.log('✅ Bootstrap Service initialized successfully');
    } catch (error) {
      console.error('❌ Bootstrap Service initialization failed:', error);
      throw error;
    }
  }

  private async initializeDatabase(): Promise<void> {
    console.log('📊 Initializing database connection...');
    this.updateServiceStatus('database', 'starting');
    
    try {
      // Simulate database initialization
      await new Promise(resolve => setTimeout(resolve, 100));
      this.updateServiceStatus('database', 'running');
      console.log('✅ Database connection established');
    } catch (error) {
      this.updateServiceStatus('database', 'error', error.message);
      throw error;
    }
  }

  private async initializeRedis(): Promise<void> {
    console.log('🔴 Initializing Redis connection...');
    this.updateServiceStatus('redis', 'starting');
    
    try {
      // Simulate Redis initialization
      await new Promise(resolve => setTimeout(resolve, 50));
      this.updateServiceStatus('redis', 'running');
      console.log('✅ Redis connection established');
    } catch (error) {
      this.updateServiceStatus('redis', 'error', error.message);
      throw error;
    }
  }

  private async initializeServices(): Promise<void> {
    console.log('⚙️ Initializing application services...');
    
    for (const serviceName of this.config.services) {
      this.updateServiceStatus(serviceName, 'starting');
      
      try {
        // Simulate service initialization
        await new Promise(resolve => setTimeout(resolve, 25));
        this.updateServiceStatus(serviceName, 'running');
        console.log(`✅ Service ${serviceName} initialized`);
      } catch (error) {
        this.updateServiceStatus(serviceName, 'error', error.message);
        console.error(`❌ Service ${serviceName} failed to initialize:`, error);
      }
    }
  }

  private updateServiceStatus(name: string, status: ServiceStatus['status'], error?: string): void {
    this.services.set(name, {
      name,
      status,
      lastCheck: new Date(),
      error,
    });
  }

  getServiceStatus(name: string): ServiceStatus | undefined {
    return this.services.get(name);
  }

  getAllServiceStatuses(): ServiceStatus[] {
    return Array.from(this.services.values());
  }

  async healthCheck(): Promise<{ healthy: boolean; services: ServiceStatus[] }> {
    const services = this.getAllServiceStatuses();
    const healthy = services.every(service => service.status === 'running');

    return {
      healthy,
      services,
    };
  }

  async shutdown(): Promise<void> {
    console.log('🛑 Shutting down Bootstrap Service...');
    
    for (const [name] of this.services) {
      this.updateServiceStatus(name, 'stopped');
    }

    this.isInitialized = false;
    console.log('✅ Bootstrap Service shutdown complete');
  }

  isReady(): boolean {
    return this.isInitialized && this.getAllServiceStatuses().every(
      service => service.status === 'running'
    );
  }
}

export default BootstrapService;
