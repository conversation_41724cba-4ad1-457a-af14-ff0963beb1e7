#!/usr/bin/env node

/**
 * Staging Connection Validator
 * Validates connectivity to staging server and services before running tests
 */

const https = require('https');
const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');
const path = require('path');

// Load test environment
dotenv.config({ path: path.join(__dirname, '../.env.test') });

const STAGING_CONFIG = {
  serverUrl: process.env.STAGING_SERVER_URL || 'https://mvs.kanousai.com',
  supabaseUrl: process.env.STAGING_SUPABASE_URL || 'https://hiyqiqbgiueyyvqoqhht.supabase.co',
  supabaseAnonKey: process.env.STAGING_SUPABASE_ANON_KEY,
  supabaseServiceKey: process.env.STAGING_SUPABASE_SERVICE_ROLE_KEY,
  testVendorEmail: process.env.STAGING_TEST_VENDOR_EMAIL || '<EMAIL>',
  testAdminEmail: process.env.STAGING_TEST_ADMIN_EMAIL || '<EMAIL>',
};

console.log('🔍 MVS-VR Staging Connection Validator');
console.log('=====================================');

async function checkHttpsEndpoint(url, name) {
  return new Promise(resolve => {
    const startTime = Date.now();

    https
      .get(url, res => {
        const responseTime = Date.now() - startTime;
        console.log(`✅ ${name}: ${res.statusCode} (${responseTime}ms)`);
        resolve({ success: true, status: res.statusCode, responseTime });
      })
      .on('error', err => {
        const responseTime = Date.now() - startTime;
        console.log(`❌ ${name}: ${err.message} (${responseTime}ms)`);
        resolve({ success: false, error: err.message, responseTime });
      })
      .setTimeout(10000, () => {
        console.log(`⏰ ${name}: Timeout (10s)`);
        resolve({ success: false, error: 'Timeout', responseTime: 10000 });
      });
  });
}

async function checkSupabaseConnection() {
  try {
    console.log('\n🗄️  Testing Supabase connection...');

    if (!STAGING_CONFIG.supabaseAnonKey) {
      console.log('❌ Supabase anon key not configured');
      return false;
    }

    const supabase = createClient(STAGING_CONFIG.supabaseUrl, STAGING_CONFIG.supabaseAnonKey);

    // Test basic connection with available table
    const { data, error } = await supabase.from('products').select('count').limit(1);

    if (error) {
      console.log(`❌ Supabase query failed: ${error.message}`);
      return false;
    }

    console.log('✅ Supabase connection successful');
    return true;
  } catch (error) {
    console.log(`❌ Supabase connection error: ${error.message}`);
    return false;
  }
}

async function checkApiEndpoints() {
  console.log('\n🌐 Testing API endpoints...');

  const endpoints = [
    { url: `${STAGING_CONFIG.serverUrl}/health`, name: 'Health Check' },
    { url: `${STAGING_CONFIG.serverUrl}/api/auth/status`, name: 'Auth Status' },
    { url: `${STAGING_CONFIG.serverUrl}/api/vendors`, name: 'Vendors API' },
    { url: `${STAGING_CONFIG.serverUrl}/api/assets`, name: 'Assets API' },
  ];

  const results = [];

  for (const endpoint of endpoints) {
    const result = await checkHttpsEndpoint(endpoint.url, endpoint.name);
    results.push(result);
  }

  return results;
}

async function validateTestUsers() {
  console.log('\n👤 Validating test users...');

  try {
    const supabase = createClient(STAGING_CONFIG.supabaseUrl, STAGING_CONFIG.supabaseAnonKey);

    // Check available tables instead of specific test users
    const { data: profilesData, error: profilesError } = await supabase
      .from('profiles')
      .select('id, username')
      .limit(1);

    if (profilesError) {
      console.log(`❌ Error checking profiles table: ${profilesError.message}`);
      return false;
    }

    console.log(`✅ Profiles table accessible (${profilesData?.length || 0} records found)`);
    console.log(`ℹ️  Test users will be validated during actual test runs`);

    return true;
  } catch (error) {
    console.log(`❌ Error validating test users: ${error.message}`);
    return false;
  }
}

async function main() {
  console.log(`🎯 Target Environment: Staging`);
  console.log(`📡 Server: ${STAGING_CONFIG.serverUrl}`);
  console.log(`🗄️  Database: ${STAGING_CONFIG.supabaseUrl}`);
  console.log(`👤 Test Vendor: ${STAGING_CONFIG.testVendorEmail}`);
  console.log(`👤 Test Admin: ${STAGING_CONFIG.testAdminEmail}`);

  let allPassed = true;

  // Check server connectivity
  console.log('\n🌐 Testing server connectivity...');
  const serverResult = await checkHttpsEndpoint(STAGING_CONFIG.serverUrl, 'Main Server');
  if (!serverResult.success) allPassed = false;

  // Check Supabase connectivity
  const supabaseResult = await checkSupabaseConnection();
  if (!supabaseResult) allPassed = false;

  // Check API endpoints
  const apiResults = await checkApiEndpoints();
  const failedApis = apiResults.filter(r => !r.success);
  if (failedApis.length > 0) {
    console.log(`⚠️  ${failedApis.length} API endpoints failed`);
    // Don't fail completely for API endpoints as some may require auth
  }

  // Validate test users
  const usersResult = await validateTestUsers();
  if (!usersResult) {
    console.log('⚠️  Test user validation failed (tests may still work)');
  }

  // Summary
  console.log('\n📋 Validation Summary:');
  console.log('======================');

  if (allPassed) {
    console.log('✅ All critical checks passed');
    console.log('🚀 Ready to run staging tests');
    console.log('\nRun tests with: node scripts/run-staging-tests.js');
    process.exit(0);
  } else {
    console.log('❌ Some critical checks failed');
    console.log('🔧 Please fix the issues before running tests');
    console.log('\nTroubleshooting:');
    console.log('1. Check network connectivity');
    console.log('2. Verify staging server is running');
    console.log('3. Check Supabase instance status');
    console.log('4. Verify environment variables in .env.test');
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

main().catch(error => {
  console.error('❌ Validation failed:', error.message);
  process.exit(1);
});
